'use client';

import React, { useMemo, useRef, useEffect } from 'react';
import { unified } from 'unified';
import remarkParse from 'remark-parse';
import remarkGfm from 'remark-gfm';
import remarkRehype from 'remark-rehype';
import rehypeHighlight from 'rehype-highlight';
import rehypeStringify from 'rehype-stringify';
import rehypeRaw from 'rehype-raw';

interface MarkdownRendererProps {
  content: string;
  className?: string;
  initialScrollPosition?: number;
  onScrollPositionChange?: (position: number) => void;
}

export default function MarkdownRenderer({
  content,
  className = '',
  initialScrollPosition,
  onScrollPositionChange
}: MarkdownRendererProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  
  const htmlContent = useMemo(() => {
    try {
      const processor = unified()
        .use(remarkParse)
        .use(remarkGfm)
        .use(remarkRehype, { allowDangerousHtml: true })
        .use(rehypeRaw)
        .use(rehypeHighlight)
        .use(rehypeStringify);

      const result = processor.processSync(content);
      return String(result);
    } catch (error) {
      console.error('Markdown rendering error:', error);
      return '<p>渲染错误</p>';
    }
  }, [content]);

  // 恢复滚动位置
  useEffect(() => {
    if (containerRef.current && initialScrollPosition !== undefined) {
      setTimeout(() => {
        if (containerRef.current) {
          containerRef.current.scrollTop = initialScrollPosition;
        }
      }, 100);
    }
  }, [initialScrollPosition]);

  // 监听滚动事件
  useEffect(() => {
    const container = containerRef.current;
    if (!container || !onScrollPositionChange) return;

    const handleScroll = () => {
      onScrollPositionChange(container.scrollTop);
    };

    container.addEventListener('scroll', handleScroll);
    return () => container.removeEventListener('scroll', handleScroll);
  }, [onScrollPositionChange]);

  return (
    <div
      ref={containerRef}
      className={`prose prose-slate dark:prose-invert max-w-none p-4 overflow-auto h-full ${className}`}
      dangerouslySetInnerHTML={{ __html: htmlContent }}
      style={{
        lineHeight: '1.7',
        fontSize: '16px'
      }}
    />
  );
}
