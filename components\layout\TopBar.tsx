'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  FileText,
  Save,
  Upload,
  Download,
  Sun,
  Moon,
  Settings,
  Check,
  ChevronDown,
  Copy,
} from 'lucide-react';
import { ExportUtils, Platform } from '@/lib/export';
import { LoginButton } from '@/components/auth/LoginButton';

interface TopBarProps {
  isDarkMode: boolean;
  onThemeToggle: () => void;
  isSaved: boolean;
  content?: string;
  onSave?: () => void;
}

export function TopBar({ isDarkMode, onThemeToggle, isSaved, content = '', onSave }: TopBarProps) {
  const [isExporting, setIsExporting] = useState(false);

  // 处理导出
  const handleExport = async (platform: Platform) => {
    if (!content.trim()) {
      alert('没有内容可以导出');
      return;
    }

    setIsExporting(true);
    try {
      await ExportUtils.exportAsHtml(content, platform);
    } catch (error) {
      console.error('Export failed:', error);
      alert('导出失败，请重试');
    } finally {
      setIsExporting(false);
    }
  };

  // 处理复制
  const handleCopy = async (platform: Platform) => {
    if (!content.trim()) {
      alert('没有内容可以复制');
      return;
    }

    try {
      const success = await ExportUtils.copyHtmlToClipboard(content, platform);
      if (success) {
        alert('已复制到剪贴板');
      } else {
        alert('复制失败，请重试');
      }
    } catch (error) {
      console.error('Copy failed:', error);
      alert('复制失败，请重试');
    }
  };

  // 处理保存
  const handleSave = () => {
    if (onSave) {
      onSave();
    } else {
      ExportUtils.exportAsMarkdown(content);
    }
  };
  return (
    <div className="h-12 bg-background border-b border-border flex items-center justify-between px-4 relative z-50">
      {/* Left Section */}
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-2">
          <FileText className="w-5 h-5 text-primary" />
          <span className="font-semibold text-foreground">Markdown编辑器</span>
        </div>
      </div>

      {/* Center Section */}
      <div className="flex-1 flex justify-center">
        <div className="text-sm text-muted-foreground max-w-md truncate">
          未命名文档.md
        </div>
      </div>

      {/* Right Section */}
      <div className="flex items-center space-x-2">
        {/* Save Button */}
        <Button
          variant="ghost"
          size="sm"
          className="text-xs"
          onClick={handleSave}
        >
          {isSaved ? (
            <>
              <Check className="w-3 h-3 mr-1" />
              已保存
            </>
          ) : (
            <>
              <Save className="w-3 h-3 mr-1" />
              保存
            </>
          )}
        </Button>

        {/* Publish */}
        <Button size="sm" className="text-xs">
          <Upload className="w-3 h-3 mr-1" />
          发布
        </Button>

        {/* Export Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="text-xs" disabled={isExporting}>
              <Download className="w-3 h-3 mr-1" />
              {isExporting ? '导出中...' : '导出'}
              <ChevronDown className="w-3 h-3 ml-1" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => handleExport('wechat')}>
              <Download className="w-3 h-3 mr-2" />
              <span>🟢 微信公众号</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleCopy('wechat')}>
              <Copy className="w-3 h-3 mr-2" />
              <span>📋 复制微信格式</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleExport('xiaohongshu')}>
              <Download className="w-3 h-3 mr-2" />
              <span>🔴 小红书</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleCopy('xiaohongshu')}>
              <Copy className="w-3 h-3 mr-2" />
              <span>📋 复制小红书格式</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleExport('zhihu')}>
              <Download className="w-3 h-3 mr-2" />
              <span>🔵 知乎</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleCopy('zhihu')}>
              <Copy className="w-3 h-3 mr-2" />
              <span>📋 复制知乎格式</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleExport('feishu')}>
              <Download className="w-3 h-3 mr-2" />
              <span>🟡 飞书</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleCopy('feishu')}>
              <Copy className="w-3 h-3 mr-2" />
              <span>📋 复制飞书格式</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => ExportUtils.exportAsMarkdown(content)}>
              <Download className="w-3 h-3 mr-2" />
              <span>📄 导出Markdown</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Theme Toggle */}
        <Button variant="ghost" size="sm" onClick={onThemeToggle}>
          {isDarkMode ? (
            <Sun className="w-4 h-4" />
          ) : (
            <Moon className="w-4 h-4" />
          )}
        </Button>

        {/* GitHub Login */}
        <LoginButton />

        {/* Settings */}
        <Button variant="ghost" size="sm">
          <Settings className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
}