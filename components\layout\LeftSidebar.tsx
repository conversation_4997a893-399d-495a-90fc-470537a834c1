'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  List,
  FileText,
  GitBranch,
  Search,
  ChevronRight,
  ChevronDown,
  File,
  Folder,
  FolderOpen,
  History,
} from 'lucide-react';
import { GitPanel } from '@/components/git/GitPanel';
import { HistoryPanel } from '@/components/git/HistoryPanel';

interface LeftSidebarProps {
  isCollapsed: boolean;
  onToggle: () => void;
  onFileSelect?: (filepath: string) => void;
  currentFile?: string | null;
  currentFileContent?: string; // 添加当前文件内容属性
  gitStatusVersion?: number; // 添加Git状态版本控制
  onCommitContentChange?: (content: string, filename: string) => void;
  activeTab?: string; // 当前活跃的标签页
  onActiveTabChange?: (tab: string) => void; // 标签页变化回调
}

export function LeftSidebar({
  isCollapsed,
  onToggle,
  onFileSelect,
  currentFile,
  currentFileContent,
  gitStatusVersion,
  onCommitContentChange,
  activeTab: externalActiveTab,
  onActiveTabChange
}: LeftSidebarProps) {
  // 使用外部传入的activeTab，如果没有则使用本地状态
  const [localActiveTab, setLocalActiveTab] = useState('outline');
  const activeTab = externalActiveTab || localActiveTab;

  // 处理标签页切换
  const handleTabChange = (tab: string) => {
    if (onActiveTabChange) {
      onActiveTabChange(tab);
    } else {
      setLocalActiveTab(tab);
    }
  };

  const sidebarTabs = [
    { id: 'outline', icon: List, label: '大纲' },
    { id: 'files', icon: FileText, label: '文件' },
    { id: 'git', icon: GitBranch, label: 'Git' },
    { id: 'history', icon: History, label: '历史' },
    { id: 'search', icon: Search, label: '搜索' },
  ];

  return (
    <div className="flex h-full bg-secondary/30">
      {/* Icon Bar */}
      <div className="w-12 bg-secondary border-r border-border flex flex-col items-center py-2 space-y-1">
        {sidebarTabs.map((tab) => (
          <Button
            key={tab.id}
            variant={activeTab === tab.id ? 'default' : 'ghost'}
            size="sm"
            className="w-8 h-8 p-0"
            onClick={() => handleTabChange(tab.id)}
          >
            <tab.icon className="w-4 h-4" />
          </Button>
        ))}
      </div>

      {/* Content Panel */}
      {!isCollapsed && (
        <div className="w-64 bg-background border-r border-border flex flex-col">
          {/* Header */}
          <div className="h-8 flex items-center justify-between px-3 bg-secondary/50">
            <span className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
              {sidebarTabs.find((tab) => tab.id === activeTab)?.label}
            </span>
          </div>

          <Separator />

          <ScrollArea className="flex-1">
            <div className="p-2">
              {activeTab === 'outline' && <OutlinePanel />}
              {activeTab === 'files' && <FilesPanel />}
              {activeTab === 'git' && (
                <GitPanel
                  onFileSelect={onFileSelect}
                  currentFile={currentFile}
                  currentFileContent={currentFileContent}
                  gitStatusVersion={gitStatusVersion}
                />
              )}
              {activeTab === 'history' && (
                <HistoryPanel
                  onFileSelect={onFileSelect}
                  currentFile={currentFile}
                  onCommitContentChange={onCommitContentChange}
                />
              )}
              {activeTab === 'search' && <SearchPanel />}
            </div>
          </ScrollArea>
        </div>
      )}
    </div>
  );
}

function OutlinePanel() {
  return (
    <div className="space-y-1">
      <div className="text-xs text-muted-foreground mb-2">文档大纲</div>
      {[
        '介绍',
        '快速开始',
        '核心功能',
        '配置',
        '高级用法',
        'API 参考',
      ].map((item, index) => (
        <Button
          key={index}
          variant="ghost"
          size="sm"
          className="w-full justify-start text-xs font-normal h-6 px-2"
        >
          <span className="mr-2 text-muted-foreground">
            {index < 2 ? 'H1' : index < 4 ? 'H2' : 'H3'}
          </span>
          {item}
        </Button>
      ))}
    </div>
  );
}

function FilesPanel() {
  const [expandedFolders, setExpandedFolders] = useState<string[]>(() => {
    // 从localStorage恢复展开状态
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('files-panel-expanded');
      if (saved) {
        try {
          return JSON.parse(saved);
        } catch {
          return ['docs'];
        }
      }
    }
    return ['docs'];
  });

  const toggleFolder = (folder: string) => {
    setExpandedFolders((prev) => {
      const newExpanded = prev.includes(folder)
        ? prev.filter((f) => f !== folder)
        : [...prev, folder];

      // 保存到localStorage
      localStorage.setItem('files-panel-expanded', JSON.stringify(newExpanded));
      return newExpanded;
    });
  };

  return (
    <div className="space-y-1">
      <div className="text-xs text-muted-foreground mb-2">项目文件</div>
      
      <Button
        variant="ghost"
        size="sm"
        className="w-full justify-start text-xs font-normal h-6 px-2"
        onClick={() => toggleFolder('docs')}
      >
        {expandedFolders.includes('docs') ? (
          <FolderOpen className="w-3 h-3 mr-2" />
        ) : (
          <Folder className="w-3 h-3 mr-2" />
        )}
        docs
        {expandedFolders.includes('docs') ? (
          <ChevronDown className="w-3 h-3 ml-auto" />
        ) : (
          <ChevronRight className="w-3 h-3 ml-auto" />
        )}
      </Button>

      {expandedFolders.includes('docs') && (
        <div className="ml-4 space-y-1">
          {['README.md', 'guide.md', 'api.md'].map((file) => (
            <Button
              key={file}
              variant="ghost"
              size="sm"
              className="w-full justify-start text-xs font-normal h-6 px-2"
            >
              <File className="w-3 h-3 mr-2" />
              {file}
            </Button>
          ))}
        </div>
      )}
    </div>
  );
}



function SearchPanel() {
  return (
    <div className="space-y-3">
      <div className="text-xs text-muted-foreground mb-2">搜索</div>
      <input
        type="text"
        placeholder="在文件中搜索..."
        className="w-full text-xs px-2 py-1 bg-background border border-border rounded"
      />
      <div className="text-xs text-muted-foreground">
        未找到结果
      </div>
    </div>
  );
}