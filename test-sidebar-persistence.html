<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>侧边栏状态持久化测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            gap: 20px;
            height: 500px;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        .sidebar {
            display: flex;
            background: #f8f9fa;
        }
        .icon-bar {
            width: 48px;
            background: #e9ecef;
            border-right: 1px solid #ddd;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px 0;
            gap: 4px;
        }
        .tab-icon {
            width: 32px;
            height: 32px;
            border: none;
            border-radius: 4px;
            background: transparent;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }
        .tab-icon.active {
            background: #007bff;
            color: white;
        }
        .tab-icon:hover:not(.active) {
            background: #e9ecef;
        }
        .content-panel {
            width: 256px;
            background: white;
            border-right: 1px solid #ddd;
            display: flex;
            flex-direction: column;
        }
        .panel-header {
            height: 32px;
            background: #f8f9fa;
            border-bottom: 1px solid #ddd;
            display: flex;
            align-items: center;
            padding: 0 12px;
            font-size: 12px;
            font-weight: 600;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .panel-content {
            flex: 1;
            padding: 8px;
            overflow-y: auto;
        }
        .main-content {
            flex: 1;
            background: white;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
        }
        .refresh-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin-bottom: 20px;
            font-size: 16px;
        }
        .refresh-btn:hover {
            background: #218838;
        }
        .status {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #007bff;
        }
        .file-item, .outline-item {
            padding: 4px 8px;
            margin: 2px 0;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .file-item:hover, .outline-item:hover {
            background: #f8f9fa;
        }
        .file-item.active, .outline-item.active {
            background: #e3f2fd;
            color: #1976d2;
        }
        .git-status {
            padding: 8px;
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            margin: 8px 0;
            font-size: 12px;
        }
        .search-input {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 12px;
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <h1>🔄 侧边栏状态持久化测试</h1>
    <p>这个测试演示了左侧边栏标签页状态在页面刷新后的持久化功能。</p>
    
    <button class="refresh-btn" onclick="location.reload()">🔄 刷新页面测试</button>
    
    <div class="container">
        <div class="sidebar">
            <!-- Icon Bar -->
            <div class="icon-bar">
                <button class="tab-icon" data-tab="outline" title="大纲">📋</button>
                <button class="tab-icon" data-tab="files" title="文件">📁</button>
                <button class="tab-icon" data-tab="git" title="Git">🌿</button>
                <button class="tab-icon" data-tab="history" title="历史">🕒</button>
                <button class="tab-icon" data-tab="search" title="搜索">🔍</button>
            </div>
            
            <!-- Content Panel -->
            <div class="content-panel">
                <div class="panel-header">
                    <span id="panelTitle">大纲</span>
                </div>
                <div class="panel-content" id="panelContent">
                    <!-- 内容将通过JavaScript动态加载 -->
                </div>
            </div>
        </div>
        
        <div class="main-content">
            <div>
                <h3>主编辑区域</h3>
                <p>这里是主要的编辑器内容区域</p>
            </div>
        </div>
    </div>
    
    <div class="status">
        <strong>📊 状态信息：</strong>
        <div id="statusInfo"></div>
    </div>

    <script>
        // 应用状态
        let appState = {
            activeTab: 'outline',
            selectedFile: null,
            expandedFolders: ['docs'],
            searchQuery: ''
        };

        // 标签页配置
        const tabs = {
            outline: { title: '大纲', icon: '📋' },
            files: { title: '文件', icon: '📁' },
            git: { title: 'Git', icon: '🌿' },
            history: { title: '历史', icon: '🕒' },
            search: { title: '搜索', icon: '🔍' }
        };

        // 保存状态到 localStorage
        function saveState() {
            localStorage.setItem('sidebar-state', JSON.stringify(appState));
            updateStatusInfo();
        }

        // 从 localStorage 加载状态
        function loadState() {
            const saved = localStorage.getItem('sidebar-state');
            if (saved) {
                try {
                    appState = { ...appState, ...JSON.parse(saved) };
                } catch (e) {
                    console.error('加载状态失败:', e);
                }
            }
            updateStatusInfo();
        }

        // 更新状态信息显示
        function updateStatusInfo() {
            const info = document.getElementById('statusInfo');
            info.innerHTML = `
                <div><strong>活跃标签页:</strong> ${tabs[appState.activeTab].title} (${appState.activeTab})</div>
                <div><strong>选中文件:</strong> ${appState.selectedFile || '无'}</div>
                <div><strong>展开文件夹:</strong> ${appState.expandedFolders.join(', ')}</div>
                <div><strong>搜索关键词:</strong> ${appState.searchQuery || '无'}</div>
                <div><strong>保存时间:</strong> ${new Date().toLocaleTimeString()}</div>
            `;
        }

        // 切换标签页
        function switchTab(tabId) {
            appState.activeTab = tabId;
            updateTabUI();
            updatePanelContent();
            saveState();
        }

        // 更新标签页UI
        function updateTabUI() {
            // 更新图标按钮状态
            document.querySelectorAll('.tab-icon').forEach(btn => {
                btn.classList.toggle('active', btn.dataset.tab === appState.activeTab);
            });
            
            // 更新面板标题
            document.getElementById('panelTitle').textContent = tabs[appState.activeTab].title;
        }

        // 更新面板内容
        function updatePanelContent() {
            const content = document.getElementById('panelContent');
            
            switch (appState.activeTab) {
                case 'outline':
                    content.innerHTML = `
                        <div style="font-size: 12px; color: #6c757d; margin-bottom: 8px;">文档大纲</div>
                        <div class="outline-item">📄 介绍</div>
                        <div class="outline-item">🚀 快速开始</div>
                        <div class="outline-item">⚙️ 核心功能</div>
                        <div class="outline-item">🔧 配置</div>
                        <div class="outline-item">📚 高级用法</div>
                        <div class="outline-item">📖 API 参考</div>
                    `;
                    break;
                    
                case 'files':
                    content.innerHTML = `
                        <div style="font-size: 12px; color: #6c757d; margin-bottom: 8px;">项目文件</div>
                        <div class="file-item">📁 docs/</div>
                        <div class="file-item" style="margin-left: 16px;">📄 README.md</div>
                        <div class="file-item" style="margin-left: 16px;">📄 guide.md</div>
                        <div class="file-item" style="margin-left: 16px;">📄 api.md</div>
                        <div class="file-item">📄 package.json</div>
                        <div class="file-item">📄 tsconfig.json</div>
                    `;
                    break;
                    
                case 'git':
                    content.innerHTML = `
                        <div style="font-size: 12px; color: #6c757d; margin-bottom: 8px;">Git 状态</div>
                        <div class="git-status">
                            <div><strong>分支:</strong> main</div>
                            <div><strong>状态:</strong> 有未提交的更改</div>
                        </div>
                        <div style="font-size: 12px; color: #6c757d; margin: 8px 0;">更改的文件:</div>
                        <div class="file-item">📝 README.md (已修改)</div>
                        <div class="file-item">➕ new-file.md (新文件)</div>
                    `;
                    break;
                    
                case 'history':
                    content.innerHTML = `
                        <div style="font-size: 12px; color: #6c757d; margin-bottom: 8px;">提交历史</div>
                        <div class="file-item">🔹 feat: 添加状态持久化</div>
                        <div class="file-item">🔹 fix: 修复水合错误</div>
                        <div class="file-item">🔹 docs: 更新文档</div>
                        <div class="file-item">🔹 init: 初始提交</div>
                    `;
                    break;
                    
                case 'search':
                    content.innerHTML = `
                        <div style="font-size: 12px; color: #6c757d; margin-bottom: 8px;">搜索</div>
                        <input type="text" class="search-input" placeholder="在文件中搜索..." 
                               value="${appState.searchQuery}" 
                               onchange="updateSearchQuery(this.value)">
                        <div style="font-size: 12px; color: #6c757d;">
                            ${appState.searchQuery ? `搜索 "${appState.searchQuery}" 的结果:` : '输入关键词开始搜索'}
                        </div>
                        ${appState.searchQuery ? '<div class="file-item">📄 在 README.md 中找到 2 个结果</div>' : ''}
                    `;
                    break;
            }
        }

        // 更新搜索关键词
        function updateSearchQuery(query) {
            appState.searchQuery = query;
            saveState();
            updatePanelContent();
        }

        // 初始化
        function init() {
            // 加载保存的状态
            loadState();
            
            // 设置事件监听器
            document.querySelectorAll('.tab-icon').forEach(btn => {
                btn.addEventListener('click', () => switchTab(btn.dataset.tab));
            });
            
            // 更新UI
            updateTabUI();
            updatePanelContent();
            
            console.log('侧边栏状态持久化测试已初始化');
            console.log('当前状态:', appState);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
