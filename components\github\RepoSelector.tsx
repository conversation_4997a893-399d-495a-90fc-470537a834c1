'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { GitHubService, GitHubRepo } from '@/lib/github';
import { Search, GitBranch, Lock, Globe, Star, GitFork, ChevronLeft, ChevronRight } from 'lucide-react';

interface RepoSelectorProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onRepoSelected?: (repo: GitHubRepo) => void;
}

export function RepoSelector({ open, onOpenChange, onRepoSelected }: RepoSelectorProps) {
  const { data: session } = useSession();
  const [repos, setRepos] = useState<GitHubRepo[]>([]);
  const [filteredRepos, setFilteredRepos] = useState<GitHubRepo[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [sortBy, setSortBy] = useState<'name' | 'updated' | 'stars'>('updated');
  
  const itemsPerPage = 10; // 每页显示10个仓库

  useEffect(() => {
    if (open && session?.accessToken) {
      loadRepos();
      setCurrentPage(1); // 重置页码
      setSearchQuery(''); // 清空搜索
    }
  }, [open, session]);

  useEffect(() => {
    let filtered = [...repos];
    
    // 搜索过滤
    if (searchQuery) {
      filtered = repos.filter(repo =>
        repo.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        repo.description?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }
    
    // 排序
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'updated':
          return new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime();
        case 'stars':
          // 使用可选链处理stargazers_count
          return ((b as any).stargazers_count || 0) - ((a as any).stargazers_count || 0);
        default:
          return 0;
      }
    });
    
    setFilteredRepos(filtered);
    setCurrentPage(1); // 搜索后重置到第一页
  }, [searchQuery, repos, sortBy]);

  // 分页数据
  const totalPages = Math.ceil(filteredRepos.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedRepos = filteredRepos.slice(startIndex, startIndex + itemsPerPage);

  const loadRepos = async () => {
    if (!session?.accessToken) return;

    setLoading(true);
    try {
      const githubService = new GitHubService(session.accessToken);
      console.log('🔄 开始获取所有仓库...');
      const userRepos = await githubService.getAllUserRepos();
      console.log(`✅ 获取到 ${userRepos.length} 个仓库`);
      setRepos(userRepos);
    } catch (error) {
      console.error('Failed to load repos:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRepoSelect = (repo: GitHubRepo) => {
    onRepoSelected?.(repo);
    onOpenChange(false);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-[95vw] max-w-[800px] h-[85vh] max-h-[700px] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="flex items-center space-x-2">
            <GitBranch className="w-5 h-5" />
            <span>选择GitHub仓库</span>
            {repos.length > 0 && (
              <Badge variant="outline" className="ml-2">
                {repos.length} 个仓库
              </Badge>
            )}
          </DialogTitle>
          <DialogDescription>
            从您的GitHub账户中选择一个仓库进行编辑
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 flex flex-col space-y-4 min-h-0">
          {/* 搜索和排序 */}
          <div className="flex-shrink-0 space-y-3">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <Input
                placeholder="搜索仓库名称或描述..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <span className="text-sm text-muted-foreground">排序:</span>
                <div className="flex space-x-1">
                  <Button
                    variant={sortBy === 'updated' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSortBy('updated')}
                    className="text-xs h-7"
                  >
                    最近更新
                  </Button>
                  <Button
                    variant={sortBy === 'name' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSortBy('name')}
                    className="text-xs h-7"
                  >
                    名称
                  </Button>
                  <Button
                    variant={sortBy === 'stars' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSortBy('stars')}
                    className="text-xs h-7"
                  >
                    ⭐ 热度
                  </Button>
                </div>
              </div>
              
              {filteredRepos.length > 0 && (
                <div className="text-sm text-muted-foreground">
                  找到 {filteredRepos.length} 个仓库
                </div>
              )}
            </div>
          </div>

          {/* 仓库列表 */}
          <ScrollArea className="flex-1 min-h-0">
            {loading ? (
              <div className="flex items-center justify-center h-40">
                <div className="flex flex-col items-center space-y-2">
                  <div className="w-8 h-8 animate-spin rounded-full border-2 border-primary border-t-transparent" />
                  <p className="text-sm text-muted-foreground">加载仓库中...</p>
                </div>
              </div>
            ) : paginatedRepos.length > 0 ? (
              <div className="space-y-2 pr-4">
                {paginatedRepos.map((repo) => (
                  <div
                    key={repo.id}
                    className="p-4 border rounded-lg hover:bg-muted/50 cursor-pointer transition-all duration-200 hover:shadow-sm"
                    onClick={() => handleRepoSelect(repo)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 space-y-2">
                        <div className="flex items-center space-x-2">
                          <h3 className="font-medium text-sm">{repo.name}</h3>
                          <div className="flex items-center space-x-1">
                            {repo.private ? (
                              <Lock className="w-3 h-3 text-amber-600" />
                            ) : (
                              <Globe className="w-3 h-3 text-green-600" />
                            )}
                            <Badge variant={repo.private ? "destructive" : "default"} className="text-xs">
                              {repo.private ? '私有' : '公开'}
                            </Badge>
                          </div>
                        </div>
                        
                        {repo.description && (
                          <p className="text-xs text-muted-foreground line-clamp-2">
                            {repo.description}
                          </p>
                        )}
                        
                        <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                          <div className="flex items-center space-x-1">
                            <GitBranch className="w-3 h-3" />
                            <span>{repo.default_branch}</span>
                          </div>
                          {(repo as any).stargazers_count !== undefined && (repo as any).stargazers_count > 0 && (
                            <div className="flex items-center space-x-1">
                              <Star className="w-3 h-3" />
                              <span>{(repo as any).stargazers_count}</span>
                            </div>
                          )}
                          <span>更新于 {formatDate(repo.updated_at)}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center h-40 text-muted-foreground">
                <GitBranch className="w-12 h-12 mb-4 opacity-50" />
                <p className="text-sm text-center">
                  {searchQuery ? (
                    <>
                      没有找到匹配 "<span className="font-medium">{searchQuery}</span>" 的仓库
                      <br />
                      <span className="text-xs">尝试使用其他关键词搜索</span>
                    </>
                  ) : (
                    '没有找到仓库'
                  )}
                </p>
              </div>
            )}
          </ScrollArea>

          {/* 分页控件 */}
          {totalPages > 1 && (
            <div className="flex-shrink-0 flex items-center justify-between py-2 border-t">
              <div className="text-sm text-muted-foreground">
                第 {currentPage} 页，共 {totalPages} 页 
                <span className="ml-2">
                  (显示 {startIndex + 1}-{Math.min(startIndex + itemsPerPage, filteredRepos.length)} 个，
                  共 {filteredRepos.length} 个)
                </span>
              </div>
              
              <div className="flex items-center space-x-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                  className="h-8"
                >
                  <ChevronLeft className="w-4 h-4" />
                </Button>
                
                <div className="flex items-center space-x-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNum;
                    if (totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (currentPage >= totalPages - 2) {
                      pageNum = totalPages - 4 + i;
                    } else {
                      pageNum = currentPage - 2 + i;
                    }
                    
                    return (
                      <Button
                        key={pageNum}
                        variant={currentPage === pageNum ? "default" : "outline"}
                        size="sm"
                        onClick={() => setCurrentPage(pageNum)}
                        className="h-8 w-8 p-0"
                      >
                        {pageNum}
                      </Button>
                    );
                  })}
                </div>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                  className="h-8"
                >
                  <ChevronRight className="w-4 h-4" />
                </Button>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
