'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { X, Palette, Type, Space as Spacing, Eye } from 'lucide-react';

interface RightSidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

export function RightSidebar({ isOpen, onClose }: RightSidebarProps) {
  if (!isOpen) return null;

  return (
    <div className="w-80 bg-background border-l border-border flex flex-col">
      {/* Header */}
      <div className="h-12 flex items-center justify-between px-4 border-b border-border">
        <div className="flex items-center space-x-2">
          <Palette className="w-4 h-4" />
          <span className="font-medium text-sm">渲染设置</span>
        </div>
        <Button variant="ghost" size="sm" onClick={onClose}>
          <X className="w-4 h-4" />
        </Button>
      </div>

      <ScrollArea className="flex-1">
        <div className="p-4 space-y-6">
          {/* Typography Section */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Type className="w-4 h-4" />
              <Label className="text-sm font-medium">字体排版</Label>
            </div>
            
            <div className="space-y-3">
              <div className="space-y-2">
                <Label className="text-xs">字体</Label>
                <Select defaultValue="inter">
                  <SelectTrigger className="text-xs">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="inter">Inter</SelectItem>
                    <SelectItem value="roboto">Roboto</SelectItem>
                    <SelectItem value="source-serif">Source Serif Pro</SelectItem>
                    <SelectItem value="system">系统字体</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label className="text-xs">字体大小</Label>
                <Slider
                  defaultValue={[16]}
                  max={24}
                  min={12}
                  step={1}
                  className="w-full"
                />
                <div className="text-xs text-muted-foreground">16px</div>
              </div>

              <div className="space-y-2">
                <Label className="text-xs">行高</Label>
                <Slider
                  defaultValue={[1.6]}
                  max={2.5}
                  min={1.0}
                  step={0.1}
                  className="w-full"
                />
                <div className="text-xs text-muted-foreground">1.6</div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Layout Section */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Spacing className="w-4 h-4" />
              <Label className="text-sm font-medium">布局</Label>
            </div>
            
            <div className="space-y-3">
              <div className="space-y-2">
                <Label className="text-xs">内容宽度</Label>
                <Select defaultValue="medium">
                  <SelectTrigger className="text-xs">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="narrow">窄 (600px)</SelectItem>
                    <SelectItem value="medium">中等 (800px)</SelectItem>
                    <SelectItem value="wide">宽 (1200px)</SelectItem>
                    <SelectItem value="full">全宽</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label className="text-xs">段落间距</Label>
                <Slider
                  defaultValue={[1.5]}
                  max={3}
                  min={0.5}
                  step={0.1}
                  className="w-full"
                />
                <div className="text-xs text-muted-foreground">1.5em</div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Display Options */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Eye className="w-4 h-4" />
              <Label className="text-sm font-medium">显示选项</Label>
            </div>
            
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label className="text-xs">显示行号</Label>
                <Switch defaultChecked />
              </div>

              <div className="flex items-center justify-between">
                <Label className="text-xs">自动换行</Label>
                <Switch defaultChecked />
              </div>

              <div className="flex items-center justify-between">
                <Label className="text-xs">语法高亮</Label>
                <Switch defaultChecked />
              </div>

              <div className="flex items-center justify-between">
                <Label className="text-xs">实时预览</Label>
                <Switch defaultChecked />
              </div>
            </div>
          </div>

          <Separator />

          {/* Theme Customization */}
          <div className="space-y-4">
            <Label className="text-sm font-medium">配色方案</Label>
            
            <div className="grid grid-cols-3 gap-2">
              {[
                { name: '默认', color: 'bg-blue-500' },
                { name: '绿色', color: 'bg-green-500' },
                { name: '紫色', color: 'bg-purple-500' },
                { name: '橙色', color: 'bg-orange-500' },
                { name: '粉色', color: 'bg-pink-500' },
                { name: '青色', color: 'bg-teal-500' },
              ].map((theme) => (
                <Button
                  key={theme.name}
                  variant="outline"
                  size="sm"
                  className="p-2 h-auto flex flex-col space-y-1"
                >
                  <div className={`w-4 h-4 rounded ${theme.color}`}></div>
                  <span className="text-xs">{theme.name}</span>
                </Button>
              ))}
            </div>
          </div>
        </div>
      </ScrollArea>
    </div>
  );
}