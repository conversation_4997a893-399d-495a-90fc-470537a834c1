import NextAuth from 'next-auth';
import GitHub<PERSON>rovider from 'next-auth/providers/github';

const handler = NextAuth({
  debug: true, // 启用调试模式
  providers: [
    GitHubProvider({
      clientId: process.env.GITHUB_CLIENT_ID!,
      clientSecret: process.env.GITHUB_CLIENT_SECRET!,
      authorization: {
        params: {
          scope: 'read:user user:email repo'
        }
      }
    })
  ],
  callbacks: {
    async jwt({ token, account, profile }) {
      // 保存GitHub访问令牌
      if (account) {
        token.accessToken = account.access_token;
        token.refreshToken = account.refresh_token;
      }
      
      // 保存GitHub用户信息
      if (profile) {
        token.githubId = profile.id;
        token.githubLogin = profile.login;
        token.githubName = profile.name;
        token.githubEmail = profile.email;
        token.githubAvatar = profile.avatar_url;
      }
      
      return token;
    },
    async session({ session, token }) {
      // 将令牌信息传递给客户端会话
      session.accessToken = token.accessToken as string;
      session.refreshToken = token.refreshToken as string;
      
      // 添加GitHub用户信息
      session.user.githubId = token.githubId as string;
      session.user.githubLogin = token.githubLogin as string;
      session.user.githubName = token.githubName as string;
      session.user.githubEmail = token.githubEmail as string;
      session.user.githubAvatar = token.githubAvatar as string;
      
      return session;
    }
  },
  session: {
    strategy: 'jwt'
  }
});

export { handler as GET, handler as POST };
