mvp 提示词
你是一个优秀的前端工程师，擅长制作前端页面，你要帮我完成一个项目mvp，暂时不用考虑功能实现，将界面设计好就可以，项目如下
技术栈
Next.js (App Router)
TypeScript
React
Tailwind CSS
CodeMirror 6
isomorphic-git & lightning-fs
Dexie.js
unified (Remark/Rehype)
Shiki 或 Prism.js
核心功能
1. 可切换为源码模式/实时渲染模式，目标：写作时聚焦，阅读时干净
- 源码模式：展示用户输入的内容，即markdown源码
- 实时渲染模式：
  - 当光标定位到某一段时，该段落切换为markdown源码
  - 当光标移除某一段时，该段落切换为html样式
2. git工作流
- 项目需介入github，用户编辑完成后发布到github上
- 每次保存即commit、回滚、diff一步到位
- 可远程管理github内容
3. 导出为 —— 微信 / 小红书 / 知乎 / 飞书 排版映射  
  导出功能确保html样式能在多平台兼容复制
4. 快捷键支持
5. 浅色/暗色主题
6. 渲染样式设置
排版布局（如果有遗漏帮我补充）
1. 顶栏（固定顶端）
  - 左侧
  Logo
  - 中央
  文档标题
  - 右侧
    1. 保存（已保存）
    2. 发布
    3. 导出为（下拉列表）
    4. 主题切换（浅色/亮色）
    5. 用户头像
    6. 设置
2. 左侧栏（可折叠）样式模仿vscode左侧栏
  1. 图标列表
    1. markdown文本目录导航
    2. 本地文件列表
    3. github列表管理器
    4. 搜索
  2. 图标展开列表

3. 中央核心区域
4. 右侧栏（渲染设置 可关闭）
5. 底部状态栏（固定底端 模仿vs code）
  1. 字数统计
  2. git分支
  
  

