'use client';

import React, { useState, useCallback, useMemo } from 'react';
import { unified } from 'unified';
import remarkParse from 'remark-parse';
import remarkGfm from 'remark-gfm';
import remarkRehype from 'remark-rehype';
import rehypeHighlight from 'rehype-highlight';
import rehypeStringify from 'rehype-stringify';
import rehypeRaw from 'rehype-raw';

interface LiveRenderModeProps {
  content: string;
  onChange: (content: string) => void;
  className?: string;
}

interface ParagraphData {
  id: string;
  markdown: string;
  html: string;
  isEditing: boolean;
}

// 渲染Markdown为HTML的函数
const renderMarkdown = (markdown: string) => {
  try {
    const processor = unified()
      .use(remarkParse)
      .use(remarkGfm)
      .use(remarkRehype, { allowDangerousHtml: true })
      .use(rehypeRaw)
      .use(rehypeHighlight)
      .use(rehypeStringify);

    const result = processor.processSync(markdown);
    return String(result);
  } catch (error) {
    console.error('Markdown rendering error:', error);
    return '<p>渲染错误</p>';
  }
};

export default function LiveRenderMode({
  content,
  onChange,
  className = ''
}: LiveRenderModeProps) {
  const [editingParagraphId, setEditingParagraphId] = useState<string | null>(null);

  // 将内容分割成段落
  const paragraphs = useMemo(() => {
    const lines = content.split('\n');
    const paragraphs: ParagraphData[] = [];
    let currentParagraph = '';
    let paragraphIndex = 0;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      // 如果是空行，结束当前段落
      if (line.trim() === '') {
        if (currentParagraph.trim() !== '') {
          paragraphs.push({
            id: `paragraph-${paragraphIndex}`,
            markdown: currentParagraph.trim(),
            html: renderMarkdown(currentParagraph.trim()),
            isEditing: editingParagraphId === `paragraph-${paragraphIndex}`
          });
          paragraphIndex++;
        }
        currentParagraph = '';
      } else {
        currentParagraph += (currentParagraph ? '\n' : '') + line;
      }
    }

    // 处理最后一个段落
    if (currentParagraph.trim() !== '') {
      paragraphs.push({
        id: `paragraph-${paragraphIndex}`,
        markdown: currentParagraph.trim(),
        html: renderMarkdown(currentParagraph.trim()),
        isEditing: editingParagraphId === `paragraph-${paragraphIndex}`
      });
    }

    return paragraphs;
  }, [content, editingParagraphId]);

  // 开始编辑段落
  const startEditing = useCallback((paragraphId: string) => {
    setEditingParagraphId(paragraphId);
  }, []);

  // 完成编辑段落
  const finishEditing = useCallback((paragraphId: string, newMarkdown: string) => {
    const paragraphIndex = parseInt(paragraphId.split('-')[1]);
    const newParagraphs = [...paragraphs];
    
    // 更新段落内容
    if (newParagraphs[paragraphIndex]) {
      newParagraphs[paragraphIndex].markdown = newMarkdown;
    }

    // 重新构建完整内容
    const newContent = newParagraphs.map(p => p.markdown).join('\n\n');
    onChange(newContent);
    setEditingParagraphId(null);
  }, [paragraphs, onChange]);

  // 取消编辑
  const cancelEditing = useCallback(() => {
    setEditingParagraphId(null);
  }, []);

  return (
    <div className={`p-4 space-y-4 ${className}`}>
      {paragraphs.map((paragraph) => (
        <ParagraphBlock
          key={paragraph.id}
          paragraph={paragraph}
          onStartEdit={() => startEditing(paragraph.id)}
          onFinishEdit={(newMarkdown) => finishEditing(paragraph.id, newMarkdown)}
          onCancelEdit={cancelEditing}
        />
      ))}
      
      {paragraphs.length === 0 && (
        <div className="text-muted-foreground text-center py-8">
          开始编写您的 Markdown 内容...
        </div>
      )}
    </div>
  );
}

interface ParagraphBlockProps {
  paragraph: ParagraphData;
  onStartEdit: () => void;
  onFinishEdit: (newMarkdown: string) => void;
  onCancelEdit: () => void;
}

function ParagraphBlock({
  paragraph,
  onStartEdit,
  onFinishEdit,
  onCancelEdit
}: ParagraphBlockProps) {
  const [editingContent, setEditingContent] = useState(paragraph.markdown);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && e.ctrlKey) {
      e.preventDefault();
      onFinishEdit(editingContent);
    } else if (e.key === 'Escape') {
      e.preventDefault();
      setEditingContent(paragraph.markdown);
      onCancelEdit();
    }
  };

  if (paragraph.isEditing) {
    return (
      <div className="border border-primary rounded-lg p-3 bg-muted/50">
        <textarea
          value={editingContent}
          onChange={(e) => setEditingContent(e.target.value)}
          onKeyDown={handleKeyDown}
          onBlur={() => onFinishEdit(editingContent)}
          className="w-full h-24 resize-none border-none outline-none bg-transparent font-mono text-sm"
          placeholder="编辑段落内容... (Ctrl+Enter保存, Esc取消)"
          autoFocus
        />
        <div className="text-xs text-muted-foreground mt-2">
          Ctrl+Enter 保存 • Esc 取消
        </div>
      </div>
    );
  }

  return (
    <div
      className="prose prose-sm max-w-none dark:prose-invert cursor-pointer hover:bg-muted/30 rounded-lg p-3 transition-colors"
      onClick={onStartEdit}
      dangerouslySetInnerHTML={{ __html: paragraph.html }}
    />
  );
}
