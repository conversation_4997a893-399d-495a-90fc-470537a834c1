<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>状态持久化测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            gap: 20px;
            height: 400px;
        }
        .sidebar {
            width: 200px;
            background: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
        }
        .editor {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        .tabs {
            display: flex;
            gap: 5px;
            margin-bottom: 10px;
        }
        .tab {
            padding: 5px 10px;
            background: #e0e0e0;
            border: none;
            cursor: pointer;
            border-radius: 3px;
        }
        .tab.active {
            background: #007bff;
            color: white;
        }
        .content {
            flex: 1;
            display: flex;
            gap: 10px;
        }
        .editor-pane, .preview-pane {
            flex: 1;
            border: 1px solid #ddd;
            padding: 10px;
            overflow-y: auto;
            height: 300px;
        }
        textarea {
            width: 100%;
            height: 100%;
            border: none;
            resize: none;
            font-family: monospace;
        }
        .file-item {
            padding: 5px;
            cursor: pointer;
            border-radius: 3px;
        }
        .file-item:hover {
            background: #e0e0e0;
        }
        .file-item.active {
            background: #007bff;
            color: white;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            background: #f0f0f0;
            border-radius: 5px;
        }
        .refresh-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>页面刷新状态持久化测试</h1>
    <p>这个测试页面演示了如何在页面刷新后保持应用状态。</p>
    
    <button class="refresh-btn" onclick="location.reload()">🔄 刷新页面测试</button>
    
    <div class="container">
        <div class="sidebar">
            <h3>文件列表</h3>
            <div id="fileList">
                <div class="file-item" data-file="README.md">README.md</div>
                <div class="file-item" data-file="docs.md">docs.md</div>
                <div class="file-item" data-file="notes.md">notes.md</div>
            </div>
        </div>
        
        <div class="editor">
            <div class="tabs">
                <button class="tab" data-mode="source">源码</button>
                <button class="tab" data-mode="preview">预览</button>
                <button class="tab" data-mode="split">分屏</button>
            </div>
            
            <div class="content">
                <div class="editor-pane" id="editorPane">
                    <textarea id="editor" placeholder="开始编写 Markdown..."></textarea>
                </div>
                <div class="preview-pane" id="previewPane" style="display: none;">
                    <div id="preview"></div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="status">
        <strong>状态信息：</strong>
        <div id="statusInfo"></div>
    </div>

    <script>
        // 应用状态
        let appState = {
            currentFile: 'README.md',
            editorMode: 'source',
            content: '',
            scrollPosition: { editor: 0, preview: 0 }
        };

        // 文件内容模拟
        const fileContents = {
            'README.md': '# 项目说明\n\n这是一个测试项目。\n\n## 功能\n\n- 状态持久化\n- 滚动位置保存\n- 文件切换',
            'docs.md': '# 文档\n\n## 安装\n\n```bash\nnpm install\n```\n\n## 使用\n\n```bash\nnpm start\n```',
            'notes.md': '# 笔记\n\n## 今日任务\n\n- [ ] 完成状态持久化功能\n- [ ] 测试滚动位置保存\n- [x] 创建测试页面'
        };

        // 保存状态到 localStorage
        function saveState() {
            localStorage.setItem('app-state', JSON.stringify(appState));
            updateStatusInfo();
        }

        // 从 localStorage 加载状态
        function loadState() {
            const saved = localStorage.getItem('app-state');
            if (saved) {
                appState = { ...appState, ...JSON.parse(saved) };
            }
            updateStatusInfo();
        }

        // 更新状态信息显示
        function updateStatusInfo() {
            const info = document.getElementById('statusInfo');
            info.innerHTML = `
                <div>当前文件: ${appState.currentFile}</div>
                <div>编辑器模式: ${appState.editorMode}</div>
                <div>内容长度: ${appState.content.length}</div>
                <div>编辑器滚动位置: ${appState.scrollPosition.editor}</div>
                <div>预览滚动位置: ${appState.scrollPosition.preview}</div>
            `;
        }

        // 切换文件
        function selectFile(filename) {
            // 保存当前文件内容
            if (appState.currentFile) {
                fileContents[appState.currentFile] = appState.content;
            }
            
            appState.currentFile = filename;
            appState.content = fileContents[filename] || '';
            
            // 更新UI
            updateFileList();
            updateEditor();
            saveState();
        }

        // 切换编辑器模式
        function setEditorMode(mode) {
            appState.editorMode = mode;
            updateTabs();
            updateEditorLayout();
            saveState();
        }

        // 更新文件列表UI
        function updateFileList() {
            const items = document.querySelectorAll('.file-item');
            items.forEach(item => {
                item.classList.toggle('active', item.dataset.file === appState.currentFile);
            });
        }

        // 更新标签页UI
        function updateTabs() {
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => {
                tab.classList.toggle('active', tab.dataset.mode === appState.editorMode);
            });
        }

        // 更新编辑器内容
        function updateEditor() {
            const editor = document.getElementById('editor');
            editor.value = appState.content;
            
            // 恢复滚动位置
            setTimeout(() => {
                editor.scrollTop = appState.scrollPosition.editor;
            }, 100);
        }

        // 更新编辑器布局
        function updateEditorLayout() {
            const editorPane = document.getElementById('editorPane');
            const previewPane = document.getElementById('previewPane');
            
            switch (appState.editorMode) {
                case 'source':
                    editorPane.style.display = 'block';
                    previewPane.style.display = 'none';
                    break;
                case 'preview':
                    editorPane.style.display = 'none';
                    previewPane.style.display = 'block';
                    updatePreview();
                    break;
                case 'split':
                    editorPane.style.display = 'block';
                    previewPane.style.display = 'block';
                    updatePreview();
                    break;
            }
        }

        // 更新预览内容
        function updatePreview() {
            const preview = document.getElementById('preview');
            // 简单的 Markdown 渲染
            let html = appState.content
                .replace(/^# (.*$)/gim, '<h1>$1</h1>')
                .replace(/^## (.*$)/gim, '<h2>$1</h2>')
                .replace(/^### (.*$)/gim, '<h3>$1</h3>')
                .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
                .replace(/\*(.*)\*/gim, '<em>$1</em>')
                .replace(/\n/gim, '<br>');
            
            preview.innerHTML = html;
            
            // 恢复滚动位置
            setTimeout(() => {
                preview.scrollTop = appState.scrollPosition.preview;
            }, 100);
        }

        // 初始化
        function init() {
            loadState();
            
            // 设置事件监听器
            document.querySelectorAll('.file-item').forEach(item => {
                item.addEventListener('click', () => selectFile(item.dataset.file));
            });
            
            document.querySelectorAll('.tab').forEach(tab => {
                tab.addEventListener('click', () => setEditorMode(tab.dataset.mode));
            });
            
            const editor = document.getElementById('editor');
            editor.addEventListener('input', () => {
                appState.content = editor.value;
                if (appState.editorMode === 'split') {
                    updatePreview();
                }
                saveState();
            });
            
            // 监听滚动事件
            editor.addEventListener('scroll', () => {
                appState.scrollPosition.editor = editor.scrollTop;
                saveState();
            });
            
            const preview = document.getElementById('preview');
            preview.addEventListener('scroll', () => {
                appState.scrollPosition.preview = preview.scrollTop;
                saveState();
            });
            
            // 更新UI
            updateFileList();
            updateTabs();
            updateEditor();
            updateEditorLayout();
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
