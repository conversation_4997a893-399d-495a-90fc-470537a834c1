// 应用状态接口
export interface AppState {
  currentFile: string | null;
  isLeftSidebarCollapsed: boolean;
  isRightSidebarOpen: boolean;
  isViewingCommit: boolean;
  content: string;
  scrollPosition?: {
    editor?: number;
    preview?: number;
  };
  leftSidebarActiveTab?: string; // 左侧边栏活跃标签页
}

// 简单的本地存储工具类
export class LocalStorage {
  private static readonly CONTENT_KEY = 'markdown-editor-content';
  private static readonly SETTINGS_KEY = 'markdown-editor-settings';
  private static readonly APP_STATE_KEY = 'markdown-editor-app-state';

  // 保存内容
  static saveContent(content: string): void {
    try {
      localStorage.setItem(this.CONTENT_KEY, content);
    } catch (error) {
      console.error('Failed to save content:', error);
    }
  }

  // 加载内容
  static loadContent(): string {
    try {
      return localStorage.getItem(this.CONTENT_KEY) || '';
    } catch (error) {
      console.error('Failed to load content:', error);
      return '';
    }
  }

  // 保存设置
  static saveSettings(settings: Record<string, any>): void {
    try {
      localStorage.setItem(this.SETTINGS_KEY, JSON.stringify(settings));
    } catch (error) {
      console.error('Failed to save settings:', error);
    }
  }

  // 加载设置
  static loadSettings(): Record<string, any> {
    try {
      const settings = localStorage.getItem(this.SETTINGS_KEY);
      return settings ? JSON.parse(settings) : {};
    } catch (error) {
      console.error('Failed to load settings:', error);
      return {};
    }
  }

  // 保存应用状态
  static saveAppState(state: AppState): void {
    try {
      localStorage.setItem(this.APP_STATE_KEY, JSON.stringify(state));
    } catch (error) {
      console.error('Failed to save app state:', error);
    }
  }

  // 加载应用状态
  static loadAppState(): AppState | null {
    try {
      const state = localStorage.getItem(this.APP_STATE_KEY);
      return state ? JSON.parse(state) : null;
    } catch (error) {
      console.error('Failed to load app state:', error);
      return null;
    }
  }

  // 清除所有数据
  static clear(): void {
    try {
      localStorage.removeItem(this.CONTENT_KEY);
      localStorage.removeItem(this.SETTINGS_KEY);
      localStorage.removeItem(this.APP_STATE_KEY);
    } catch (error) {
      console.error('Failed to clear storage:', error);
    }
  }
}

// 自动保存Hook
import { useEffect, useRef, useState } from 'react';

export function useAutoSave(content: string, delay: number = 1000) {
  const timeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    // 清除之前的定时器
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // 设置新的定时器
    timeoutRef.current = setTimeout(() => {
      LocalStorage.saveContent(content);
    }, delay);

    // 清理函数
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [content, delay]);

  // 组件卸载时立即保存
  useEffect(() => {
    return () => {
      LocalStorage.saveContent(content);
    };
  }, [content]);
}

// 应用状态管理Hook
export function useAppState(initialState: Partial<AppState>) {
  // 默认状态
  const defaultState: AppState = {
    currentFile: null,
    isLeftSidebarCollapsed: false,
    isRightSidebarOpen: true,
    isViewingCommit: false,
    content: '',
    ...initialState
  };

  // 初始化时使用默认状态，避免水合错误
  const [state, setState] = useState<AppState>(defaultState);
  const [isLoaded, setIsLoaded] = useState(false);

  // 在客户端加载保存的状态
  useEffect(() => {
    if (typeof window !== 'undefined' && !isLoaded) {
      const savedState = LocalStorage.loadAppState();
      if (savedState) {
        setState(prev => ({
          ...prev,
          ...savedState,
          ...initialState // 允许传入的初始状态覆盖保存的状态
        }));
      }
      setIsLoaded(true);
    }
  }, [initialState, isLoaded]);

  // 保存滚动位置
  const saveScrollPosition = (editorPos?: number, previewPos?: number) => {
    setState(prev => ({
      ...prev,
      scrollPosition: {
        ...prev.scrollPosition,
        editor: editorPos !== undefined ? editorPos : prev.scrollPosition?.editor,
        preview: previewPos !== undefined ? previewPos : prev.scrollPosition?.preview
      }
    }));
  };

  // 更新状态并保存
  const updateState = (newState: Partial<AppState>) => {
    setState(prev => {
      const updatedState = { ...prev, ...newState };
      // 异步保存到localStorage
      setTimeout(() => {
        LocalStorage.saveAppState(updatedState);
      }, 0);
      return updatedState;
    });
  };

  // 在页面刷新前保存状态
  useEffect(() => {
    const handleBeforeUnload = () => {
      LocalStorage.saveAppState(state);
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [state]);

  return { state, updateState, saveScrollPosition };
}

// 滚动位置管理Hook
export function useScrollPosition(elementRef: React.RefObject<HTMLElement>, key: string) {
  const [scrollPosition, setScrollPosition] = useState(0);

  // 保存滚动位置
  const saveScrollPosition = () => {
    if (elementRef.current) {
      const position = elementRef.current.scrollTop;
      setScrollPosition(position);
      localStorage.setItem(`scroll-${key}`, position.toString());
    }
  };

  // 恢复滚动位置
  const restoreScrollPosition = () => {
    if (elementRef.current) {
      const savedPosition = localStorage.getItem(`scroll-${key}`);
      if (savedPosition) {
        const position = parseInt(savedPosition, 10);
        elementRef.current.scrollTop = position;
        setScrollPosition(position);
      }
    }
  };

  // 监听滚动事件
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const handleScroll = () => {
      const position = element.scrollTop;
      setScrollPosition(position);
      // 防抖保存
      clearTimeout((handleScroll as any).timeout);
      (handleScroll as any).timeout = setTimeout(() => {
        localStorage.setItem(`scroll-${key}`, position.toString());
      }, 100);
    };

    element.addEventListener('scroll', handleScroll);
    return () => {
      element.removeEventListener('scroll', handleScroll);
      clearTimeout((handleScroll as any).timeout);
    };
  }, [elementRef, key]);

  return { scrollPosition, saveScrollPosition, restoreScrollPosition };
}
